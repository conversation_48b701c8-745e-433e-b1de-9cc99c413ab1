export const orders = `
  query Orders {
    restaurantOrders {
      _id
      orderId
      orderAmount
      restaurantId
      restaurantName
      orderStatus
      customerPhone
      deliveryAddress
      deliveryInstructions
      items {
        _id
        title
        description
        image
        quantity
        variation {
          _id
          title
          price
          discounted
        }
        addons {
          _id
          options {
            _id
            title
            description
            price
          }
          description
          title
          quantityMinimum
          quantityMaximum
        }
        specialInstructions
        isActive
        createdAt
        updatedAt
      }
      paymentMethod
      paidAmount
      tipping
      taxationAmount
      status
      paymentStatus
      reason
      isActive
      createdAt
      orderDate
      deliveryCharges
      isPickedUp
      preparationTime
      acceptedAt
      isRinged
    }
  }
`

export const configuration = `
  query Configuration {
    configuration {
      _id
      currency
      currencySymbol
    }
  }
`

export const restaurantInfo = `
  query Restaurant($id: String) {
    restaurant(id: $id) {
      _id
      orderId
      orderPrefix
      name
      image
      address
      location {
        coordinates
      }
      deliveryTime
      username
      isAvailable
      notificationToken
      enableNotification
      openingTimes {
        day
        times {
          startTime
          endTime
        }
      }
    }
  }
`
